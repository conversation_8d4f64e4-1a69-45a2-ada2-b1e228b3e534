package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"path"
	"strings"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/task"
	"github.com/hibiken/asynq"
)

var (
	_tencentCloudLogic *TencentCloudLogic
)

type TencentCloudLogic struct {
	tencentCloudService *service.TencentCloudService
	h5AdminUserService  *service.H5AdminAuthService
}

func SingletonTencentCloudLogic() *TencentCloudLogic {
	if _tencentCloudLogic == nil {
		_tencentCloudLogic = &TencentCloudLogic{
			tencentCloudService: service.SingletonTencentCloudService(),
			h5AdminUserService:  service.SingletonH5AdminAuthService(),
		}
	}
	return _tencentCloudLogic
}

// VerifyRealName 获取实名核身结果
func (l *TencentCloudLogic) VerifyRealName(ctx context.Context, req *bean.VerifyRealNameReq) (*bean.VerifyRealNameResp, error) {
	// 校验req.UserID在数据库中是否合法
	if err := l.h5AdminUserService.ValidateUserID(ctx, req.UserID); err != nil {
		return nil, err
	}

	code, msg, err := l.tencentCloudService.VerifyRealName(ctx, req)
	if err != nil {
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "VerifyRealName idcard: %s, code: %s, msg: %s", req.IDCard, code, msg)

	// 更新用户实名认证状态
	if err := l.h5AdminUserService.UpdateUserRealNameAuth(ctx, req.UserID, code); err != nil {
		logger.Logger.WarnfCtx(ctx, "更新用户实名认证状态失败: %v", err)
	}

	// 计算是否未成年并更新状态
	isMinors, age, err := l.h5AdminUserService.UpdateUserMinorsStatus(ctx, req.UserID, req.IDCard)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "更新用户未成年状态失败: %v", err)
	}

	return &bean.VerifyRealNameResp{
		IsRealName: code == "0",
		IsMinors:   isMinors,
		Age:        age,
	}, nil
}

// FaceFusion 人脸融合 - 异步处理（使用QPS限制队列）
func (l *TencentCloudLogic) FaceFusion(ctx context.Context, req *bean.FaceFusionReq) (*bean.FaceFusionAsyncResp, error) {
	// 参数验证
	if req.ModelID == "" {
		return nil, fmt.Errorf("model_id is required")
	}
	if req.GameID == "" {
		return nil, fmt.Errorf("game_id is required")
	}
	if req.UserID == "" {
		return nil, fmt.Errorf("user_id is required")
	}

	// 验证MergeInfos格式
	if len(req.MergeInfos) == 0 {
		return nil, fmt.Errorf("merge_infos is required")
	}
	for i, item := range req.MergeInfos {
		url, exists := item["Url"]
		if !exists {
			return nil, fmt.Errorf("invalid merge_infos format: item %d missing 'Url' field", i)
		}
		if url == "" {
			return nil, fmt.Errorf("invalid merge_infos format: item %d 'Url' cannot be empty", i)
		}

		// 验证图片格式
		ext := strings.ToLower(path.Ext(url))
		if ext == "" {
			return nil, fmt.Errorf("invalid merge_infos format: item %d 'Url' missing file extension", i)
		}
		if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
			return nil, fmt.Errorf("invalid merge_infos format: item %d 'Url' must be jpg, jpeg, or png format", i)
		}
	}

	// 验证ProjectID是否在配置中
	if config.GlobConfig.FaceFusion.ProjectID == "" {
		return nil, fmt.Errorf("project_id not configured")
	}

	// 设置默认值
	if req.RspImgType == "" {
		req.RspImgType = "url" // 默认返回URL
	}

	// 设置LogoAdd默认值为0
	if req.LogoAdd == nil {
		logoAdd := int64(0)
		req.LogoAdd = &logoAdd
	}

	// 优先使用前端传入的 TaskID（handler 层已经将其标记为必传），
	// 如果为空则回退到生成一个新的 taskID（保持对内部调用和测试的兼容性）
	var taskID string
	if req.TaskID != "" {
		taskID = req.TaskID
	} else {
		taskID = fmt.Sprintf("facefusion_%s_%s_%d", req.GameID, req.UserID, time.Now().UnixNano())
	}

	// 转换为新的任务请求格式
	taskReq := &bean.FaceFusionTaskRequest{
		GameID:            req.GameID,
		UserID:            req.UserID,
		ModelID:           req.ModelID,
		MergeInfos:        req.MergeInfos,
		FuseFaceDegree:    req.FuseFaceDegree,
		FuseProfileDegree: req.FuseProfileDegree,
		LogoAdd:           req.LogoAdd,
		// LogoParam:         req.LogoParam,
		// FuseParam:         req.FuseParam,
		RspImgType:  req.RspImgType,
		TaskID:      taskID,
		SubmittedAt: time.Now(),
		Priority:    0, // 默认优先级
	}

	// 将请求序列化为任务数据
	taskData, err := json.Marshal(taskReq)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "FaceFusion marshal task data failed: %v", err)
		return nil, fmt.Errorf("marshal task data failed: %w", err)
	}

	// 提交异步任务
	_, err = task.Submit(asynq.NewTask(task.TypeFaceFusion, taskData))
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "FaceFusion submit task failed: %v", err)
		return nil, fmt.Errorf("submit face fusion task failed: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "FaceFusion task submitted successfully: game_id=%s, model_id=%s, user_id=%s, task_id=%s",
		req.GameID, req.ModelID, req.UserID, taskID)

	// 返回任务提交成功的响应
	return &bean.FaceFusionAsyncResp{
		TaskID: taskID,
		Status: "queued", // 更新状态为已排队
	}, nil
}
