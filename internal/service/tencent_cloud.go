package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/jinzhu/copier"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/regions"
	facefusion "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/facefusion/v20220927"
	faceid "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/faceid/v20180301"
	tms "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tms/v20201229"
)

var (
	_tencentCloudOnce    sync.Once
	_tencentCloudService *TencentCloudService
)

type TencentCloudService struct {
	client           *tms.Client
	clientFaceID     *faceid.Client
	clientFaceFusion *facefusion.Client
}

func SingletonTencentCloudService() *TencentCloudService {
	_tencentCloudOnce.Do(func() {
		credential := common.NewCredential(
			config.GlobConfig.TencentCloud.SecretID,
			config.GlobConfig.TencentCloud.SecretKey,
		)
		client, err := tms.NewClient(credential, regions.Beijing, profile.NewClientProfile())
		if err != nil {
			// panic(err) 不可以使用panic影响正常流程
			logger.Logger.Errorf("!!! SingletonTencentCloudService init error, tencent cloud client error: %v", err)
			return
		}

		cpf := profile.NewClientProfile()
		cpf.HttpProfile.Endpoint = "faceid.tencentcloudapi.com"
		clientFaceID, err := faceid.NewClient(credential, "ap-guangzhou", cpf)
		if err != nil {
			// panic(err) 不可以使用panic影响正常流程
			logger.Logger.Errorf("!!! SingletonTencentCloudService init error, tencent cloud face id client error: %v", err)
			return
		}

		// 初始化人脸融合客户端
		cpfFaceFusion := profile.NewClientProfile()
		cpfFaceFusion.HttpProfile.Endpoint = "facefusion.tencentcloudapi.com"
		clientFaceFusion, err := facefusion.NewClient(credential, "ap-beijing", cpfFaceFusion)
		if err != nil {
			// panic(err) 不可以使用panic影响正常流程
			logger.Logger.Errorf("!!! SingletonTencentCloudService init error, tencent cloud face fusion client error: %v", err)
			return
		}

		_tencentCloudService = &TencentCloudService{
			client:           client,
			clientFaceID:     clientFaceID,
			clientFaceFusion: clientFaceFusion,
		}
	})
	return _tencentCloudService
}

// FatchTencentCloudCheckText
func (s *TencentCloudService) FatchTencentCloudCheckText(ctx context.Context, content, platformType string) (*bean.VerifySensitiveMessageRes, error) {
	// content to base64
	base64Content := base64.StdEncoding.EncodeToString([]byte(content))
	resp, err := s.reqSensitiveCheckText(ctx, base64Content)
	if err != nil {
		return nil, err
	}

	// 将resp转换成VerifySensitiveMessageRes
	platformDetails := make([]*bean.SensitiveMessagePlatformDetail, 0)
	for _, keyword := range resp.Response.Keywords {
		platformDetails = append(platformDetails, &bean.SensitiveMessagePlatformDetail{
			Level:   1, // 可以根据实际需求调整级别
			Keyword: keyword,
		})
	}

	// 转换建议结果
	result := bean.WechatSecurityCheckResult{
		Suggest: resp.Response.Suggestion,
		Label:   int(resp.Response.Score), // 使用Score作为label，可以根据实际需求调整
		// ReplacedContent: content,
		// 将此词语替换成*
	}

	// Keywords.
	replacedContent := content
	for _, keyword := range resp.Response.Keywords {
		stars := strings.Repeat("*", len([]rune(keyword))) // 使用 []rune 来正确处理中文字符
		replacedContent = strings.Replace(replacedContent, keyword, stars, -1)
	}
	result.ReplacedContent = replacedContent

	// 将detailResults转换成TencentCloudCheckTextDetailResp
	detailResults := make([]*bean.TencentCloudCheckTextDetailResp, 0)
	for _, detail := range resp.Response.DetailResults {
		detailResult := &bean.TencentCloudCheckTextDetailResp{}
		err := copier.Copy(detailResult, detail)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "FatchTencentCloudCheckText tencent cloud copy detail error: %v", err)
			return nil, err
		}
		detailResults = append(detailResults, detailResult)
	}

	verifySensitiveMessageRes := &bean.VerifySensitiveMessageRes{
		TraceID:         resp.Response.RequestId,
		Source:          "tencent_cloud",
		ErrCode:         int32(resp.Retcode),
		ErrMsg:          s.getErrMsg(resp.Retcode, resp.Retmsg),
		Result:          result,
		Detail:          detailResults,
		PlatformDetail:  platformDetails,
		ReplacedContent: replacedContent,
	}

	return verifySensitiveMessageRes, nil
}

func (s *TencentCloudService) getErrMsg(retcode int, retmsg string) string {
	if retcode == 0 {
		return "ok"
	}
	return retmsg
}

func (s *TencentCloudService) reqSensitiveCheckText(ctx context.Context, content string) (*bean.TencentCloudCheckTextResp, error) {
	request := tms.NewTextModerationRequest()
	request.Content = common.StringPtr(content)
	request.BizType = common.StringPtr(config.GlobConfig.TencentCloud.BizType)

	response, err := s.client.TextModeration(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Logger.ErrorfCtx(ctx, "RequestSensitiveCheckText tencent cloud text moderation error: %v", err)
		return nil, err
	}
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "RequestSensitiveCheckText tencent cloud error: %v", err)
		return nil, err
	}

	resp := &bean.TencentCloudCheckTextResp{}
	err = json.Unmarshal([]byte(response.ToJsonString()), resp)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "RequestSensitiveCheckText tencent cloud unmarshal error: %v", err)
		return nil, err
	}
	return resp, nil
}

// VerifyRealName 获取实名核身结果
func (s *TencentCloudService) VerifyRealName(ctx context.Context, req *bean.VerifyRealNameReq) (string, string, error) {
	// 创建请求
	request := faceid.NewIdCardVerificationRequest()
	request.IdCard = common.StringPtr(req.IDCard)
	request.Name = common.StringPtr(req.Name)

	// 发送请求
	response, err := s.clientFaceID.IdCardVerification(request)
	if err != nil {
		return "", "", err
	}
	if response.Response == nil {
		logger.Logger.ErrorfCtx(ctx, "VerifyRealName tencent cloud error: %v", err)
		return "", "", fmt.Errorf("response is nil, err: %v", err)
	}
	if response.Response.Result != nil && *response.Response.Result != "0" {
		logger.Logger.WarnfCtx(ctx, "VerifyRealName tencent cloud request id: %v", response.Response.RequestId)
	}

	return *response.Response.Result, *response.Response.Description, nil
}

// FaceFusion 人脸融合
func (s *TencentCloudService) FaceFusion(ctx context.Context, req *bean.FaceFusionReq) (*bean.FaceFusionResp, error) {
	if s == nil || s.clientFaceFusion == nil {
		return nil, fmt.Errorf("face fusion client not initialized")
	}

	var cancel context.CancelFunc
	ctx, cancel = context.WithTimeout(ctx, time.Duration(10)*time.Second)
	defer cancel()

	start := time.Now()

	if err := validateFaceFusionReq(req); err != nil {
		return nil, err
	}

	request, buildErr := buildFaceFusionRequest(req)
	if buildErr != nil {
		return nil, buildErr
	}

	response, err := s.clientFaceFusion.FuseFaceWithContext(ctx, request)
	if err != nil {
		if sdkErr, ok := err.(*errors.TencentCloudSDKError); ok {
			logger.Logger.ErrorfCtx(ctx, "FaceFusion SDK error code=%s msg=%s model=%s template_url=%s cost=%v", sdkErr.GetCode(), sdkErr.GetMessage(), req.ModelID, config.GlobConfig.FaceFusion.TemplateURL, time.Since(start))
			return nil, fmt.Errorf("tencent face fusion sdk error: %s %s", sdkErr.GetCode(), sdkErr.GetMessage())
		}
		logger.Logger.ErrorfCtx(ctx, "FaceFusion request error: %v model=%s template_url=%s cost=%v", err, req.ModelID, config.GlobConfig.FaceFusion.TemplateURL, time.Since(start))
		return nil, err
	}
	if response.Response == nil {
		logger.Logger.ErrorfCtx(ctx, "FaceFusion empty response model=%s cost=%v", req.ModelID, time.Since(start))
		return nil, fmt.Errorf("face fusion empty response")
	}

	resp := &bean.FaceFusionResp{RequestID: ptrToString(response.Response.RequestId)}
	if response.Response.FusedImage != nil {
		resp.FusedImage = *response.Response.FusedImage
	}

	logger.Logger.InfofCtx(ctx, "FaceFusion success request_id=%s model=%s img_len=%d cost=%v", resp.RequestID, req.ModelID, len(resp.FusedImage), time.Since(start))
	return resp, nil
}

// validateFaceFusionReq 基础参数校验（不做过多业务侵入）
func validateFaceFusionReq(req *bean.FaceFusionReq) error {
	if req == nil {
		return fmt.Errorf("req is nil")
	}
	if strings.TrimSpace(req.ModelID) == "" {
		return fmt.Errorf("model_id empty")
	}
	// MergeInfos的验证已经在logic层处理
	if req.FuseFaceDegree != nil {
		if *req.FuseFaceDegree < 0 || *req.FuseFaceDegree > 100 {
			return fmt.Errorf("fuse_face_degree out of range [0,100]")
		}
	}
	if req.FuseProfileDegree != nil {
		if *req.FuseProfileDegree < 0 || *req.FuseProfileDegree > 1 { // 按注释：0关闭1开启
			return fmt.Errorf("fuse_profile_degree out of range {0,1}")
		}
	}
	if req.LogoAdd != nil {
		if *req.LogoAdd != 0 && *req.LogoAdd != 1 {
			return fmt.Errorf("logo_add must be 0 or 1")
		}
	}
	return nil
}

// buildFaceFusionRequest 构建腾讯云请求
func buildFaceFusionRequest(req *bean.FaceFusionReq) (*facefusion.FuseFaceRequest, error) {
	r := facefusion.NewFuseFaceRequest()
	// 使用 ModelID 指定素材模板
	r.ModelId = common.StringPtr(req.ModelID)

	// 从配置获取ProjectID
	projectID := strings.TrimSpace(config.GlobConfig.FaceFusion.ProjectID)
	if projectID != "" {
		r.ProjectId = common.StringPtr(projectID)
	}

	// MergeInfos 用于传入用户人脸图片；模板通过 ModelId 选择，避免误把模板 URL 当作用户图。
	var mergeInfos []*facefusion.MergeInfo
	for _, item := range req.MergeInfos {
		if url, exists := item["Url"]; exists && url != "" {
			mergeInfo := &facefusion.MergeInfo{Image: common.StringPtr(url)}
			mergeInfos = append(mergeInfos, mergeInfo)
		}
	}

	imgType := "url"
	r.RspImgType = &imgType

	if req.FuseProfileDegree != nil {
		r.FuseProfileDegree = req.FuseProfileDegree
	}
	if req.FuseFaceDegree != nil {
		r.FuseFaceDegree = req.FuseFaceDegree
	}
	if req.LogoAdd != nil {
		r.LogoAdd = req.LogoAdd
	}
	return r, nil
}

func ptrToString(p *string) string {
	if p == nil {
		return ""
	}
	return *p
}
