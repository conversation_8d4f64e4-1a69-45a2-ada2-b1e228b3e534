package bean

import "time"

type TencentCloudCheckTextResp struct {
	Response TencentCloudCheckTextResponseData `json:"Response"`
	Retcode  int                               `json:"retcode"`
	Retmsg   string                            `json:"retmsg"`
}

type TencentCloudCheckTextResponseData struct {
	BizType           string                              `json:"BizType"`
	ContextText       string                              `json:"ContextText"`
	DataId            string                              `json:"DataId"`
	DetailResults     []TencentCloudCheckTextDetailResult `json:"DetailResults"`
	Extra             string                              `json:"Extra"`
	Keywords          []string                            `json:"Keywords"`
	Label             string                              `json:"Label"`
	RequestId         string                              `json:"RequestId"`
	RiskDetails       interface{}                         `json:"RiskDetails"`
	Score             int                                 `json:"Score"`
	SentimentAnalysis TencentCloudCheckTextSentiment      `json:"SentimentAnalysis"`
	SubLabel          string                              `json:"SubLabel"`
	Suggestion        string                              `json:"Suggestion"`
}

type TencentCloudCheckTextDetailResult struct {
	HitInfos   []TencentCloudHitInfo   `json:"HitInfos"`
	Keywords   []string                `json:"Keywords"`
	Label      string                  `json:"Label"`
	LibId      string                  `json:"LibId"`
	LibName    string                  `json:"LibName"`
	LibType    int                     `json:"LibType"`
	Score      int                     `json:"Score"`
	SubLabel   string                  `json:"SubLabel"`
	Suggestion string                  `json:"Suggestion"`
	Tags       []TencentCloudTagResult `json:"Tags"`
}

type TencentCloudTagResult struct {
	Keyword  string `json:"Keyword"`  // 命中的关键词
	SubLabel string `json:"SubLabel"` // 子标签
	Score    int    `json:"Score"`    // 子标签对应的分数
}

type TencentCloudCheckTextDetailResp struct {
	HitInfos   []TencentCloudHitInfoResp `json:"hit_infos"`
	Keywords   []string                  `json:"keywords"`
	Label      string                    `json:"label"`
	LibId      string                    `json:"lib_id"`
	LibName    string                    `json:"lib_name"`
	LibType    int                       `json:"lib_type"`
	Score      int                       `json:"score"`
	SubLabel   string                    `json:"sub_llabel"`
	Suggestion string                    `json:"suggestion"`
	Tags       []TencentCloudTagResp     `json:"tags"`
}

type TencentCloudTagResp struct {
	Keyword  string `json:"keyword"`   // 命中的关键词
	SubLabel string `json:"sub_label"` // 子标签
	Score    int    `json:"score"`     // 子标签对应的分数
}

type TencentCloudHitInfo struct {
	Keyword   string                 `json:"Keyword"`
	LibName   string                 `json:"LibName"`
	Positions []TencentCloudPosition `json:"Positions"`
	Type      string                 `json:"Type"`
}

type TencentCloudHitInfoResp struct {
	Keyword   string                     `json:"keyword"`
	LibName   string                     `json:"lib_name"`
	Positions []TencentCloudPositionResp `json:"positions"`
	Type      string                     `json:"type"`
}

type TencentCloudPosition struct {
	End   int `json:"End"`
	Start int `json:"Start"`
}

type TencentCloudPositionResp struct {
	End   int `json:"end"`
	Start int `json:"start"`
}

type TencentCloudCheckTextSentiment struct {
	Code    interface{} `json:"Code"`
	Detail  interface{} `json:"Detail"`
	Label   interface{} `json:"Label"`
	Message interface{} `json:"Message"`
	Score   interface{} `json:"Score"`
}

// 实名核身鉴权请求
type DetectAuthReq struct {
	RuleId      string `json:"rule_id" form:"rule_id" binding:"required"`           // 业务流程ID
	IdCard      string `json:"id_card" form:"id_card" binding:"required"`           // 身份证号
	Name        string `json:"name" form:"name" binding:"required"`                 // 姓名
	RedirectUrl string `json:"redirect_url" form:"redirect_url" binding:"required"` // 重定向地址
	Extra       string `json:"extra" form:"extra"`                                  // 额外信息
}

// 实名核身鉴权响应
type DetectAuthResp struct {
	BizToken  string `json:"biz_token"`  // 业务流水号
	Url       string `json:"url"`        // 重定向URL
	RequestId string `json:"request_id"` // 请求ID
	Retcode   int    `json:"retcode"`    // 返回码
	Retmsg    string `json:"retmsg"`     // 返回信息
}

// 获取实名核身结果请求
type VerifyRealNameReq struct {
	UserID string `json:"user_id" form:"user_id" binding:"required"` // 用户ID
	IDCard string `json:"id_card" form:"id_card" binding:"required"` // 身份证号
	Name   string `json:"name" form:"name" binding:"required"`       // 姓名
}

// 获取实名核身结果响应
type VerifyRealNameResp struct {
	IsRealName bool `json:"is_real_name"` // 是否实名
	IsMinors   bool `json:"is_minors"`    // 是否未成年
	Age        int  `json:"age"`          // 年龄
}

// 人脸融合请求
type FaceFusionReq struct {
	GameID string `json:"game_id" form:"game_id" binding:"required"` // 游戏ID
	UserID string `json:"user_id" form:"user_id" binding:"required"` // 用户ID
	TaskID string `json:"task_id" form:"task_id" binding:"required"` // 前端传递的任务ID，必传

	ModelID           string              `json:"model_id" form:"model_id" binding:"required"`    // 素材ID，字符串类型
	RspImgType        string              `json:"rsp_img_type" form:"rsp_img_type"`               // 返回图片类型：url 或 base64，默认url
	MergeInfos        []map[string]string `json:"merge_infos" form:"merge_infos"`                 // 融合参数
	FuseProfileDegree *int64              `json:"fuse_profile_degree" form:"fuse_profile_degree"` // 侧脸融合开关，0关闭，1开启
	FuseFaceDegree    *int64              `json:"fuse_face_degree" form:"fuse_face_degree"`       // 人脸融合度，取值范围[0,100]，默认为50
	LogoAdd           *int64              `json:"logo_add" form:"logo_add"`                       // 水印开关，0关闭，1开启
	// LogoParam         string              `json:"logo_param" form:"logo_param"`                   // 水印参数
	// FuseParam         string              `json:"fuse_param" form:"fuse_param"`                   // 融合参数
}

// 人脸融合响应
type FaceFusionResp struct {
	FusedImage string `json:"fused_image"` // 融合后的图片，base64编码或URL
	RequestID  string `json:"request_id"`  // 请求ID
}

// 人脸融合异步任务响应
type FaceFusionAsyncResp struct {
	TaskID string `json:"task_id"` // 任务ID
	Status string `json:"status"`  // 任务状态：submitted
}

// 人脸融合内部响应结构（对应腾讯云API响应）
type TencentFaceFusionResp struct {
	Response TencentFaceFusionResponseData `json:"Response"`
}

type TencentFaceFusionResponseData struct {
	FusedImage string `json:"FusedImage"` // 融合后的图片，base64编码或URL
	RequestId  string `json:"RequestId"`  // 请求ID
	Error      *struct {
		Code    string `json:"Code"`
		Message string `json:"Message"`
	} `json:"Error,omitempty"`
}

// FaceFusionTaskRequest 人脸融合任务请求 - 专用于任务处理
type FaceFusionTaskRequest struct {
	// 基础信息
	GameID  string `json:"game_id" form:"game_id" binding:"required"`   // 游戏ID
	UserID  string `json:"user_id" form:"user_id" binding:"required"`   // 用户ID
	ModelID string `json:"model_id" form:"model_id" binding:"required"` // 素材模板ID

	// 融合参数
	MergeInfos        []map[string]string `json:"merge_infos" form:"merge_infos" binding:"required"` // 用户人脸图片信息
	FuseFaceDegree    *int64              `json:"fuse_face_degree" form:"fuse_face_degree"`          // 人脸融合度，取值范围[0,100]
	FuseProfileDegree *int64              `json:"fuse_profile_degree" form:"fuse_profile_degree"`    // 五官融合度，取值范围[0,1]
	LogoAdd           *int64              `json:"logo_add" form:"logo_add"`                          // 是否添加logo，0不添加，1添加
	LogoParam         string              `json:"logo_param" form:"logo_param"`                      // 水印参数
	FuseParam         string              `json:"fuse_param" form:"fuse_param"`                      // 融合参数

	// 返回设置
	RspImgType string `json:"rsp_img_type" form:"rsp_img_type"` // 返回图片类型：base64/url

	// 任务元数据
	TaskID      string    `json:"task_id"`      // 任务ID
	SubmittedAt time.Time `json:"submitted_at"` // 提交时间
	Priority    int       `json:"priority"`     // 任务优先级，默认为0
}

// FaceFusionTaskResult 人脸融合任务结果
type FaceFusionTaskResult struct {
	TaskID      string    `json:"task_id"`      // 任务ID
	GameID      string    `json:"game_id"`      // 游戏ID
	UserID      string    `json:"user_id"`      // 用户ID
	ModelID     string    `json:"model_id"`     // 素材模板ID
	ProjectID   string    `json:"project_id"`   // 项目ID
	FusedImage  string    `json:"fused_image"`  // 融合后的图片
	RequestID   string    `json:"request_id"`   // 腾讯云请求ID
	Status      string    `json:"status"`       // 处理状态：success/failed
	Message     string    `json:"message"`      // 状态消息
	ProcessedAt time.Time `json:"processed_at"` // 处理完成时间
}

// FaceFusionCallbackTask 人脸融合回调任务
type FaceFusionCallbackTask struct {
	Attempt     int                   `json:"attempt"`      // 重试次数
	GameID      string                `json:"game_id"`      // 游戏ID
	CallbackURL string                `json:"callback_url"` // 回调URL
	Result      *FaceFusionTaskResult `json:"result"`       // 任务结果
	ScheduledAt time.Time             `json:"scheduled_at"` // 调度时间
}

// ========== Legacy Structures (for backward compatibility) ==========

// 人脸融合回调请求 - 保持向后兼容
type FaceFusionCallbackReq struct {
	Attempt                int                     `json:"attempt"`
	GameID                 string                  `json:"game_id"`
	CallbackURL            string                  `json:"callback_url"`
	FaceFusionCallbackData *FaceFusionCallbackData `json:"face_fusion_callback_data"`
}

// 人脸融合回调数据 - 保持向后兼容
type FaceFusionCallbackData struct {
	ProjectID  string `json:"project_id"`  // 活动ID
	ModelID    string `json:"model_id"`    // 素材ID
	FusedImage string `json:"fused_image"` // 融合后的图片
	RequestID  string `json:"request_id"`  // 请求ID
	Status     string `json:"status"`      // 处理状态：success/failed
	Message    string `json:"message"`     // 状态消息
}

// 人脸融合回调响应
type FaceFusionCallbackResp struct {
	Code int32  `json:"code"`
	Msg  string `json:"msg"`
}
