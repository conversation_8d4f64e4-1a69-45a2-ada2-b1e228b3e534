# 预签名URL上传功能使用指南

## 概述

本功能基于腾讯云COS的预签名URL机制，允许用户获取预签名的上传URL，然后直接通过HTTP请求上传文件到对象存储，而无需在服务端处理文件数据。

## 主要优势

1. **安全性**: 通过预签名URL限制上传权限和时效性
2. **性能**: 避免文件数据通过服务端中转，直接上传到COS
3. **灵活性**: 用户可以自行设置请求头部，如Content-Type等
4. **可控性**: 可以设置签名有效期，防止URL被滥用

## API接口

### 1. GetPresignedUploadURL

生成基础的预签名上传URL。

```go
func (s *UploadService) GetPresignedUploadURL(ctx context.Context, fileName string, expiration time.Duration) (string, error)
```

**参数说明:**

- `ctx`: 上下文
- `fileName`: 文件在COS中的路径，如"/test/example.jpg"  
- `expiration`: 签名有效期，如time.Hour表示1小时

**返回值:**

- 预签名URL字符串
- 错误信息

### 2. GetPresignedUploadURLWithOptions

生成带有特定选项的预签名上传URL，可以预设请求头部和查询参数。

```go
func (s *UploadService) GetPresignedUploadURLWithOptions(ctx context.Context, fileName string, expiration time.Duration, options *cos.PresignedURLOptions) (string, error)
```

**参数说明:**

- `options`: 预签名选项，包含Header和Query字段

## 使用示例

### 服务端生成预签名URL

```go
// 基础用法
presignedURL, err := uploadService.GetPresignedUploadURL(ctx, "/uploads/image.jpg", time.Hour)
if err != nil {
    // 处理错误
}

// 带选项用法
options := &cos.PresignedURLOptions{
    Header: &http.Header{},
}
options.Header.Set("Content-Type", "image/jpeg")

presignedURL, err := uploadService.GetPresignedUploadURLWithOptions(ctx, "/uploads/image.jpg", time.Hour, options)
```

### 客户端使用预签名URL上传

```go
// 准备文件数据
fileData := []byte("文件内容")

// 创建HTTP请求
req, err := http.NewRequest(http.MethodPut, presignedURL, bytes.NewReader(fileData))
if err != nil {
    // 处理错误
}

// 设置请求头部（用户可自行设置）
req.Header.Set("Content-Type", "image/jpeg")
req.Header.Set("Cache-Control", "max-age=3600")

// 发送请求
client := &http.Client{Timeout: 30 * time.Second}
resp, err := client.Do(req)
if err != nil {
    // 处理错误
}
defer resp.Body.Close()

if resp.StatusCode == http.StatusOK {
    // 上传成功
}
```

## 实际应用场景

### UploadImageFromURL函数的改进

原来的`UploadImageFromURL`函数已经修改为使用预签名URL方式：

1. 从指定URL下载图片数据
2. 生成用于上传的预签名URL
3. 将文件信息保存到数据库
4. 返回预签名URL给调用方

调用方可以使用返回的预签名URL直接上传文件到COS，并可以自行设置请求头部。

## 注意事项

1. **签名有效期**: 建议设置合理的有效期，既要满足使用需求，又要避免安全风险
2. **文件路径**: 确保文件路径符合COS的命名规范
3. **请求头部**: 如果在生成预签名URL时指定了特定的请求头部，客户端上传时必须包含相同的头部
4. **错误处理**: 客户端上传时要妥善处理HTTP错误响应

## 相关文档

- [腾讯云COS预签名URL文档](https://cloud.tencent.com/document/product/436/35059)
- [cos-go-sdk-v5 GitHub仓库](https://github.com/tencentyun/cos-go-sdk-v5)
